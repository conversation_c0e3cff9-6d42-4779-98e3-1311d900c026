<template>
  <div class="layout-padding">
    <el-card shadow="hover" class="chat-main-card">
      <!-- 页面头部 -->
      <template #header>
        <div class="card-header">
          <div>
            <h2 style="margin: 0; display: flex; align-items: center; gap: 8px;">
              <el-icon><ChatDotRound /></el-icon>
              AI智能体聊天
            </h2>
            <p style="margin: 8px 0 0 0; color: var(--el-text-color-regular); font-size: 14px;">
              与AI智能体进行对话，支持工具调用和知识库查询
            </p>
          </div>
          <el-space>
            <!-- 智能体连接状态指示器 -->
            <el-tag :type="connectionStatus.type" size="default">
              <el-icon><Connection /></el-icon>
              {{ connectionStatus.text }}
            </el-tag>

            <el-button @click="clearChat" :icon="Delete" type="danger" plain>
              清空对话
            </el-button>
            <el-button @click="exportChat" :icon="Download">
              导出对话
            </el-button>
          </el-space>
        </div>
      </template>

      <!-- 聊天主体区域 -->
      <div class="chat-container">
        <!-- 消息显示区域 -->
        <div class="chat-messages" ref="messagesContainer">
          <div v-if="messages.length === 0" class="empty-chat">
            <el-empty description="开始您的智能体对话吧！">
              <template #image>
                <el-icon size="60" color="var(--el-color-primary)">
                  <ChatDotRound />
                </el-icon>
              </template>
              <div style="display: flex; gap: 12px; flex-wrap: wrap; justify-content: center;">
                <el-button type="primary" @click="sendSampleMessage">
                  发送示例消息
                </el-button>
                <el-button type="success" @click="sendCalculatorTest">
                  测试计算器工具
                </el-button>
                <el-button type="info" @click="sendKnowledgeTest">
                  测试知识库查询
                </el-button>
                <el-button type="warning" @click="sendEChartsTest">
                  测试图表渲染
                </el-button>
              </div>
            </el-empty>
          </div>

          <!-- 消息列表 -->
          <template v-for="(message, index) in messages" :key="message.id || index">
            <!-- 用户消息 -->
            <div v-if="message.type === 'user'" class="message-item">
              <Bubble
                :content="message.content"
                placement="end"
                variant="filled"
                shape="round"
                :max-width="'70%'"
                avatar="我"
              >
                <template #footer>
                  <div class="message-actions">
                    <el-button
                      size="small"
                      text
                      :icon="CopyDocument"
                      @click="copyMessage(message.content)"
                      title="复制"
                    />
                  </div>
                </template>
              </Bubble>
            </div>

            <!-- AI消息 -->
            <div v-else class="message-item">
              <!-- 工具调用过程显示 -->
              <div v-if="message.toolCalls && message.toolCalls.length > 0" class="tool-calls-container">
                <ThoughtChain
                  :thinkingItems="formatToolCallsForThoughtChain(message.toolCalls)"
                  maxWidth="100%"
                  @handleExpand="onToolCallExpand"
                >
                  <template #icon="{ item }">
                    <el-icon v-if="item.status === 'success'" style="color: #67c23a;">
                      <Check />
                    </el-icon>
                    <el-icon v-else-if="item.status === 'error'" style="color: #f56c6c;">
                      <Close />
                    </el-icon>
                    <el-icon v-else-if="item.status === 'loading'" style="color: #409eff;">
                      <Loading />
                    </el-icon>
                    <el-icon v-else style="color: #909399;">
                      <Tools />
                    </el-icon>
                  </template>
                </ThoughtChain>
              </div>

              <!-- AI回复气泡 -->
              <Bubble
                placement="start"
                variant="filled"
                shape="round"
                :max-width="'70%'"
                :avatar="getAgentAvatar(message)"
                :loading="message.loading"
                :typing="message.isStreaming ? { step: 2, interval: 50, suffix: '|' } : false"
              >
                <!-- 消息头部信息 -->
                <template #header>
                  <div class="message-header-info">
                    <span class="agent-name">{{ getAgentDisplayName(message) }}</span>
                    <el-tag v-if="message.mode" :type="getModeTagType(message.mode)" size="small">
                      {{ getModeDisplayName(message.mode) }}
                    </el-tag>
                    <span class="message-time">{{ formatTime(message.timestamp) }}</span>
                  </div>
                  <!-- 思考过程组件 -->
                  <Thinking
                    v-if="message.thinkingContent"
                    :content="message.thinkingContent"
                    :status="message.thinkingStatus || 'start'"
                    :model-value="true"
                    auto-collapse
                    max-width="100%"
                    class="thinking-chain-wrap"
                  />
                </template>

                <!-- 自定义内容：支持 Markdown 渲染 -->
                <template #content>
                  <div v-if="!message.loading" class="ai-message-content">
                    <!-- Markdown 渲染 -->
                    <XMarkdown
                      v-if="message.isMarkdown && message.content"
                      :markdown="message.content"
                      :enable-code-line-number="true"
                      :enable-latex="true"
                      :enable-animate="true"
                      :enable-breaks="true"
                      :code-x-render="codeXRender"
                      :mermaid-config="mermaidConfig"
                      :need-view-code-btn="true"
                      :secure-view-code="false"
                      :view-code-modal-options="{
                        mode: 'drawer',
                        dialogOptions: {
                          closeOnClickModal: true,
                          closeOnPressEscape: true
                        }
                      }"
                      code-highlight-theme="github-light"
                      default-theme-mode="light"
                    />
                    <!-- 普通文本 -->
                    <div v-else-if="message.content" class="plain-text">
                      {{ message.content }}
                    </div>
                  </div>
                </template>

                <template #footer>
                  <div v-if="!message.loading" class="message-actions">
                    <el-button
                      size="small"
                      text
                      :icon="CopyDocument"
                      @click="copyMessage(message.content)"
                      title="复制"
                    />
                    <el-button
                      size="small"
                      text
                      :icon="Connection"
                      @click="handleRegenerateMessage(message)"
                      title="重新生成"
                    />
                  </div>
                </template>
              </Bubble>
            </div>
          </template>
        </div>

        <!-- 输入区域 -->
        <div class="chat-input-area">
          <EditorSender
            ref="editorSenderRef"
            placeholder="请输入您的问题..."
            :loading="isLoading"
            :disabled="isLoading"
            clearable
            auto-focus
            submit-type="enter"
            @submit="handleEditorSubmit"
          />
        </div>
      </div>
    </el-card>
  </div>
</template>

<script setup lang="ts" name="AiLlmChat">
import { ref, onMounted, onUnmounted, nextTick, computed, watch, reactive } from 'vue';
import { ElMessage, ElMessageBox } from 'element-plus';
import {
  ChatDotRound,
  Connection,
  Delete,
  Download,
  Promotion,
  Loading,
  User,
  CopyDocument,
  Check,
  Close,
  Tools
} from '@element-plus/icons-vue';
import {
  generateSessionId,
  clearChatHistory as clearChatHistoryAPI
} from '@/api/ai/chat';
import { aiConfig, chatConfig, aiUtils, errorMessages, successMessages } from '@/config/aiConfig';
import { AuthDebugger } from '@/utils/authDebug';
// 导入智能体API
import {
  unifiedChatStream,
  getAuthorizationHeader,
  type StreamChatEvent,
  type UnifiedChatRequest
} from '@/api/ai/unified-chat';
import { fastApiRequest } from '/@/config/knowledgeBase';
import {
  generateAgentSessionId,
  getAvailableTools,
  type ToolCall as APIToolCall
} from '@/api/ai/agent';
// 导入 Element Plus X 组件
import { Bubble, EditorSender, Typewriter, XMarkdown, Thinking, ThoughtChain } from 'vue-element-plus-x';
import { h } from 'vue';
// 导入自定义图表组件
import EChartsRenderer from '@/components/charts/EChartsRenderer.vue';

// 工具调用类型定义
interface ToolCall {
  id: string;
  name: string;
  arguments: Record<string, any>;
  result?: any;
  status: 'pending' | 'loading' | 'success' | 'error';
  error?: string;
  startTime?: number;
  endTime?: number;
}

// 消息类型定义
interface ChatMessage {
  id: string;
  type: 'user' | 'ai';
  content: string;
  timestamp: number;
  loading?: boolean;
  liked?: boolean;
  isStreaming?: boolean;
  thinking?: boolean; // 思考状态
  thinkingContent?: string; // 思考过程内容
  thinkingStatus?: 'start' | 'thinking' | 'end' | 'error'; // 思考状态
  isMarkdown?: boolean; // 是否为Markdown内容
  mode?: 'auto' | 'ai_only' | 'agent'; // 聊天模式
  toolCalls?: ToolCall[]; // 工具调用信息
  executionSummary?: string; // 执行摘要
}

// 响应式数据
const inputMessage = ref('');
const isLoading = ref(false);
const messages = ref<ChatMessage[]>([]);
const messagesContainer = ref<HTMLElement>();
const editorSenderRef = ref();
const sessionId = ref<string>(aiUtils.generateSessionId());
const chatMode = ref<'agent'>('agent'); // 固定使用智能体模式

// XMarkdown 图表配置
const mermaidConfig = {
  showToolbar: true,
  showFullscreen: true,
  showZoomIn: true,
  showZoomOut: true,
  showReset: true,
  showDownload: true,
  toolbarStyle: {
    background: 'var(--el-bg-color)',
    borderRadius: '6px',
    boxShadow: '0 2px 8px rgba(0, 0, 0, 0.1)'
  },
  iconColor: 'var(--el-text-color-primary)',
  tabTextColor: 'var(--el-text-color-primary)'
};

// 自定义代码块渲染（支持图表）- 按照官方标准实现
const codeXRender = {
  // 支持 ECharts 图表 - 使用独立组件
  echarts: (props: { raw: any }) => {
    return h(EChartsRenderer, {
      code: props.raw.content,
      theme: 'light'
    });
  },

  // 支持饼图（使用 ECharts）
  pie: (props: { raw: any }) => {
    return h(EChartsRenderer, {
      code: props.raw.content,
      theme: 'light'
    });
  }
};
// 智能体服务健康状态
const agentHealthStatus = ref<'checking' | 'healthy' | 'unhealthy' | 'unreachable'>('checking');

// 连接状态
const connectionStatus = computed(() => {
  switch (agentHealthStatus.value) {
    case 'healthy':
      return {
        type: 'success' as const,
        text: '已连接'
      };
    case 'unhealthy':
      return {
        type: 'warning' as const,
        text: '连接异常'
      };
    case 'unreachable':
      return {
        type: 'danger' as const,
        text: '连接失败'
      };
    case 'checking':
    default:
      return {
        type: 'info' as const,
        text: '检查中...'
      };
  }
});

// 生成唯一ID
const generateId = (): string => {
  return Date.now().toString(36) + Math.random().toString(36).substring(2);
};

// 解析AI回复中的思考内容
const parseAIResponse = (content: string) => {
  // 匹配完整的 <think>...</think> 标签
  const completeThinkRegex = /<think>([\s\S]*?)<\/think>/g;
  const completeMatches = content.match(completeThinkRegex);

  // 检查是否有未完成的 <think> 标签
  const incompleteThinkMatch = content.match(/<think>([\s\S]*?)$/);

  let thinkingContent = '';
  let actualContent = content;
  let hasThinking = false;
  let isThinkingComplete = true;

  // 处理完整的思考标签
  if (completeMatches) {
    thinkingContent = completeMatches
      .map(match => match.replace(/<\/?think>/g, ''))
      .join('\n\n');
    hasThinking = true;

    // 移除完整的思考标签
    actualContent = content.replace(completeThinkRegex, '').trim();
  }

  // 处理未完成的思考标签
  if (incompleteThinkMatch && !completeMatches) {
    thinkingContent = incompleteThinkMatch[1];
    hasThinking = true;
    isThinkingComplete = false;

    // 移除未完成的思考标签，保留实际内容
    actualContent = content.replace(/<think>[\s\S]*$/, '').trim();
  }

  return {
    thinkingContent,
    actualContent,
    hasThinking,
    isThinkingComplete
  };
};

// 智能生成AI回复
const generateAIResponse = (userInput: string): string => {
  const input = userInput.toLowerCase();

  // 问候语回复
  if (input.includes('你好') || input.includes('hello') || input.includes('hi')) {
    return '您好！我是AI智能助手，很高兴为您服务。我可以帮助您解答问题、提供建议、协助分析问题等。请告诉我您需要什么帮助？';
  }

  // 功能介绍
  if (input.includes('功能') || input.includes('能力') || input.includes('可以做什么')) {
    return `我是一个智能AI助手，具备以下主要功能：

🤖 **智能对话**：可以进行自然流畅的对话交流
📚 **知识问答**：回答各种领域的问题
💡 **创意协助**：提供创意想法和解决方案
📝 **文本处理**：帮助编写、修改、总结文本
🔍 **分析建议**：对问题进行分析并提供专业建议
🛠️ **技术支持**：解答技术相关问题

请随时告诉我您需要什么帮助！`;
  }

  // 技术相关问题
  if (input.includes('vue') || input.includes('前端') || input.includes('javascript') || input.includes('typescript')) {
    return `关于前端技术，我可以为您提供专业的指导：

🔧 **Vue.js**：组件开发、状态管理、路由配置等
⚡ **性能优化**：代码分割、懒加载、缓存策略
🎨 **UI框架**：Element Plus、Ant Design等组件库使用
📱 **响应式设计**：移动端适配、媒体查询
🛠️ **工程化**：Vite、Webpack、构建优化

请具体描述您遇到的问题，我会提供详细的解决方案。`;
  }

  // 学习相关
  if (input.includes('学习') || input.includes('教程') || input.includes('如何')) {
    return `很高兴您有学习的热情！我可以为您提供学习指导：

📖 **制定学习计划**：根据您的目标和时间安排
🎯 **重点知识梳理**：突出核心概念和技能
💪 **实践建议**：推荐项目和练习方式
📚 **资源推荐**：优质的学习资料和工具
🔄 **学习方法**：高效的学习策略和技巧

请告诉我您想学习什么内容，我会为您量身定制学习建议。`;
  }

  // 问题解决
  if (input.includes('问题') || input.includes('错误') || input.includes('bug') || input.includes('报错')) {
    return `我来帮您分析和解决问题：

🔍 **问题诊断**：分析问题的根本原因
🛠️ **解决方案**：提供多种可行的解决方法
⚡ **快速修复**：优先给出最直接的解决方案
🔄 **预防措施**：避免类似问题再次发生
📋 **最佳实践**：推荐标准的开发规范

请详细描述您遇到的具体问题，包括错误信息、操作步骤等，我会为您提供针对性的解决方案。`;
  }

  // 默认智能回复
  const defaultResponses = [
    `我理解您的问题。让我为您详细分析一下：

基于您提到的内容，我建议从以下几个角度来考虑：
• 首先明确核心需求和目标
• 分析现有的资源和限制条件
• 制定可行的实施方案
• 考虑可能的风险和应对措施

您希望我重点关注哪个方面呢？`,

    `这是一个很有价值的话题。根据我的分析：

🎯 **关键要点**：
• 需要综合考虑多个因素
• 建议采用渐进式的方法
• 重视实际效果和反馈

💡 **建议方案**：
• 先从小范围试点开始
• 收集数据并持续优化
• 建立长期的改进机制

您觉得这个方向如何？有什么具体的疑问吗？`,

    `感谢您的提问！让我为您提供一些专业的见解：

📊 **现状分析**：
当前的情况需要我们仔细评估各种因素的影响

🔄 **解决思路**：
• 系统性地分析问题的各个层面
• 寻找最优的平衡点
• 制定可执行的行动计划

🚀 **下一步行动**：
建议您先确定优先级，然后逐步推进

还有什么具体的细节需要我帮您分析吗？`
  ];

  return defaultResponses[Math.floor(Math.random() * defaultResponses.length)];
};

// 格式化时间
const formatTime = (timestamp: number): string => {
  const date = new Date(timestamp);
  const now = new Date();
  const diff = now.getTime() - timestamp;

  if (diff < 60000) { // 1分钟内
    return '刚刚';
  } else if (diff < 3600000) { // 1小时内
    return `${Math.floor(diff / 60000)}分钟前`;
  } else if (date.toDateString() === now.toDateString()) { // 今天
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' });
  } else {
    return date.toLocaleString('zh-CN', {
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    });
  }
};

// 滚动到底部
const scrollToBottom = async () => {
  await nextTick();
  if (messagesContainer.value) {
    messagesContainer.value.scrollTop = messagesContainer.value.scrollHeight;
  }
};

// 发送消息
const sendMessage = async () => {
  const content = inputMessage.value.trim();
  if (!content || isLoading.value) return;

  // 检查消息长度
  if (aiUtils.isMessageTooLong(content)) {
    ElMessage.error(errorMessages.tokenLimitExceeded);
    return;
  }

  // 移除AI服务状态检查，直接使用智能体服务

  // 添加用户消息
  const userMessage: ChatMessage = {
    id: generateId(),
    type: 'user',
    content,
    timestamp: Date.now()
  };
  messages.value.push(userMessage);

  // 清空输入框
  inputMessage.value = '';

  // 滚动到底部
  scrollToBottom();

  // 设置加载状态
  isLoading.value = true;

  // 添加AI消息占位符
  const aiMessage: ChatMessage = {
    id: generateId(),
    type: 'ai',
    content: '',
    timestamp: Date.now(),
    loading: true,
    isStreaming: true,
    thinking: true, // 开启思考状态
    thinkingContent: '', // 初始为空，等待解析
    thinkingStatus: 'start', // 初始状态
    isMarkdown: true // 默认启用 Markdown 渲染
  };
  messages.value.push(aiMessage);
  scrollToBottom();

  try {
    let fullResponse = '';

    // 使用流式API调用
    await chatWithAIStream(
      {
        message: content,
        session_id: sessionId.value,
        model: aiConfig.defaultModel,
        max_tokens: aiConfig.maxTokens,
        temperature: aiConfig.defaultTemperature,
        top_p: aiConfig.defaultTopP,
        stream: true
      },
      // onMessage 回调
      (streamResponse: ChatStreamResponse) => {
        fullResponse += streamResponse.delta;

        // 解析AI回复内容
        const parsed = parseAIResponse(fullResponse);

        // 调试日志
        if (import.meta.env.DEV) {
          console.log('🔍 解析结果:', {
            fullResponse: fullResponse.substring(0, 100) + '...',
            hasThinking: parsed.hasThinking,
            isThinkingComplete: parsed.isThinkingComplete,
            thinkingContent: parsed.thinkingContent.substring(0, 50) + '...',
            actualContent: parsed.actualContent.substring(0, 50) + '...'
          });
        }

        // 确定思考状态
        let thinkingStatus: 'start' | 'thinking' | 'end' | 'error' = 'start';
        if (parsed.hasThinking) {
          if (!parsed.isThinkingComplete) {
            thinkingStatus = 'thinking'; // 思考中
          } else if (parsed.actualContent) {
            thinkingStatus = 'end'; // 思考完成，有实际回复
          } else {
            thinkingStatus = 'thinking'; // 思考完成但还没有实际回复
          }
        }

        // 更新AI消息内容
        const messageIndex = messages.value.findIndex(msg => msg.id === aiMessage.id);
        if (messageIndex !== -1) {
          messages.value[messageIndex] = {
            ...aiMessage,
            content: parsed.actualContent,
            thinkingContent: parsed.thinkingContent,
            thinkingStatus: thinkingStatus,
            thinking: Boolean(parsed.hasThinking), // 有思考内容就显示思考组件
            loading: !parsed.actualContent && !parsed.hasThinking, // 没有任何内容时显示加载
            isStreaming: true,
            timestamp: Date.now()
          };
          scrollToBottom();
        }
      },
      // onError 回调
      (error: Error) => {
        console.error('AI聊天流式响应错误:', error);
        ElMessage.error(error.message || errorMessages.unknownError);

        // 移除失败的AI消息
        const messageIndex = messages.value.findIndex(msg => msg.id === aiMessage.id);
        if (messageIndex !== -1) {
          messages.value.splice(messageIndex, 1);
        }
      },
      // onComplete 回调
      () => {
        // 解析最终的AI回复内容
        const parsed = parseAIResponse(fullResponse);

        // 更新AI消息为完成状态
        const messageIndex = messages.value.findIndex(msg => msg.id === aiMessage.id);
        if (messageIndex !== -1) {
          messages.value[messageIndex] = {
            ...aiMessage,
            content: parsed.actualContent,
            thinkingContent: parsed.thinkingContent,
            thinkingStatus: 'end', // 完成状态
            thinking: Boolean(parsed.hasThinking), // 有思考内容就保持显示
            loading: false,
            isStreaming: false, // 完成后停止流式状态
            timestamp: Date.now()
          };
          scrollToBottom();
        }
      }
    );

  } catch (error: any) {
    console.error('AI聊天失败:', error);

    // 特殊处理认证错误
    if (error.message?.includes('认证失败') || error.message?.includes('未登录')) {
      ElMessage.error('认证失败，请重新登录后再试');

      // 在开发环境下打印调试信息
      if (import.meta.env.DEV) {
        console.group('🔍 认证错误调试信息');
        AuthDebugger.printDebugInfo();
        console.groupEnd();
      }
    } else {
      ElMessage.error(`发送消息失败: ${error.message || '请稍后重试'}`);
    }

    // 移除失败的AI消息
    const messageIndex = messages.value.findIndex(msg => msg.id === aiMessage.id);
    if (messageIndex !== -1) {
      messages.value.splice(messageIndex, 1);
    }
  } finally {
    isLoading.value = false;
  }
};

// 处理Shift+Enter换行
const handleShiftEnter = (event: KeyboardEvent) => {
  // 允许默认的换行行为
  return true;
};

// 发送示例消息
const sendSampleMessage = () => {
  const sampleMessages = [
    '你好，请介绍一下你的功能和能力。',
    '我在学习Vue.js，有什么好的建议吗？',
    '如何提高前端开发效率？',
    '请帮我分析一下现代Web开发的趋势。'
  ];
  const randomMessage = sampleMessages[Math.floor(Math.random() * sampleMessages.length)];
  sendMessageWithContent(randomMessage);
};

// 测试计算器工具
const sendCalculatorTest = () => {
  const testMessages = [
    '请帮我计算 123 + 456 * 789',
    '计算圆周率的前10位小数',
    '求解方程 2x + 5 = 15',
    '计算 sin(30°) + cos(60°)'
  ];
  const randomMessage = testMessages[Math.floor(Math.random() * testMessages.length)];
  sendMessageWithContent(randomMessage);
};

// 测试知识库查询
const sendKnowledgeTest = () => {
  const testMessages = [
    '查询Vue.js的生命周期钩子函数',
    '搜索JavaScript异步编程的最佳实践',
    '查找React和Vue的区别',
    '搜索前端性能优化的方法'
  ];
  const randomMessage = testMessages[Math.floor(Math.random() * testMessages.length)];
  sendMessageWithContent(randomMessage);
};

// 测试ECharts图表渲染
const sendEChartsTest = () => {
  const testMessage: ChatMessage = {
    id: generateId(),
    type: 'ai',
    content: `根据您的数据，我为您生成了一个饼图：

\`\`\`echarts
option = {
  title: {
    text: '数据分布图',
    left: 'center'
  },
  tooltip: {
    trigger: 'item'
  },
  legend: {
    orient: 'vertical',
    left: 'left'
  },
  series: [
    {
      name: '分类',
      type: 'pie',
      radius: ['20%', '50%'],
      data: [
        { value: 335, name: '类别A' },
        { value: 310, name: '类别B' },
        { value: 274, name: '类别C' },
        { value: 235, name: '类别D' },
        { value: 400, name: '类别E' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
};
\`\`\`

这个图表显示了各个类别的数据分布情况。`,
    timestamp: Date.now(),
    isMarkdown: true
  };

  messages.value.push(testMessage);
  scrollToBottom();
};

// 测试HTML代码块（验证内置查看代码功能）
const sendHtmlTest = () => {
  const testMessage: ChatMessage = {
    id: generateId(),
    type: 'ai',
    content: `这是一个HTML代码示例，应该显示"查看代码"按钮：

\`\`\`html
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>示例页面</title>
    <style>
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            font-family: Arial, sans-serif;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
        }
        .content {
            margin-top: 20px;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 8px;
        }
        .button {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            cursor: pointer;
            transition: background 0.3s;
        }
        .button:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>欢迎来到示例页面</h1>
            <p>这是一个演示HTML代码的示例</p>
        </div>
        <div class="content">
            <h2>功能特性</h2>
            <ul>
                <li>响应式设计</li>
                <li>现代化样式</li>
                <li>交互式按钮</li>
            </ul>
            <button class="button" onclick="alert('按钮被点击了！')">
                点击我
            </button>
        </div>
    </div>

    <script>
        console.log('页面加载完成');

        // 添加一些交互功能
        document.addEventListener('DOMContentLoaded', function() {
            const button = document.querySelector('.button');
            button.addEventListener('mouseover', function() {
                this.style.transform = 'scale(1.05)';
            });
            button.addEventListener('mouseout', function() {
                this.style.transform = 'scale(1)';
            });
        });
    <\/script>
</body>
</html>
\`\`\`

这个HTML代码块应该显示"查看代码"和"预览"按钮，您可以点击查看完整代码或预览效果。`,
    timestamp: Date.now(),
    isMarkdown: true
  };

  messages.value.push(testMessage);
  scrollToBottom();
};

// 复制消息
const copyMessage = async (content: string) => {
  try {
    await navigator.clipboard.writeText(content);
    ElMessage.success('消息已复制到剪贴板');
  } catch (error) {
    ElMessage.error('复制失败');
  }
};

// 点赞消息
const likeMessage = (message: ChatMessage) => {
  message.liked = !message.liked;
  ElMessage.success(message.liked ? '已点赞' : '已取消点赞');
};

// 新组件的事件处理方法
const handleCopyMessage = (content: string) => {
  ElMessage.success('内容已复制到剪贴板');
};

const handleEditMessage = (message: ChatMessage) => {
  // 将消息内容填入输入框
  inputMessage.value = message.content;
  ElMessage.info('消息内容已填入输入框，您可以编辑后重新发送');
};

const handleDeleteMessage = (message: ChatMessage) => {
  const index = messages.value.findIndex(m => m.id === message.id);
  if (index > -1) {
    messages.value.splice(index, 1);
    ElMessage.success('消息已删除');
  }
};

const handleRegenerateMessage = async (message: ChatMessage) => {
  // 找到这条AI消息对应的用户消息
  const messageIndex = messages.value.findIndex(m => m.id === message.id);
  if (messageIndex > 0) {
    const userMessage = messages.value[messageIndex - 1];
    if (userMessage.type === 'user') {
      // 删除当前AI消息
      messages.value.splice(messageIndex, 1);
      // 重新发送用户消息
      inputMessage.value = userMessage.content;
      await sendMessage();
    }
  }
};

const handleContinueConversation = () => {
  inputMessage.value = '请继续';
  sendMessage();
};

const handleTypingComplete = (message: ChatMessage) => {
  message.isStreaming = false;
  scrollToBottom();
};

// 停止生成
const stopGeneration = () => {
  isLoading.value = false;
  ElMessage.info('已停止生成');
};

// Element Plus X EditorSender 相关方法
const handleEditorSubmit = (submitResult: any) => {
  const content = submitResult.text?.trim();
  if (!content || isLoading.value) return;

  // 发送消息
  sendMessageWithContent(content);

  // 清空编辑器
  editorSenderRef.value?.clear();
};

// 使用内容发送消息的通用方法
const sendMessageWithContent = async (content: string) => {
  if (!content.trim() || isLoading.value) return;

  // 添加用户消息
  const userMessage: ChatMessage = {
    id: generateId(),
    type: 'user',
    content: content.trim(),
    timestamp: Date.now()
  };

  messages.value.push(userMessage);
  scrollToBottom();

  // 添加AI消息占位符
  const aiMessage: ChatMessage = {
    id: generateId(),
    type: 'ai',
    content: '',
    timestamp: Date.now(),
    loading: true,
    isStreaming: true,
    thinking: false,
    thinkingContent: '',
    thinkingStatus: 'start',
    isMarkdown: true,
    mode: chatMode.value,
    toolCalls: [],
    executionSummary: ''
  };
  messages.value.push(aiMessage);
  scrollToBottom();

  isLoading.value = true;
  let fullResponse = '';

  try {
    await unifiedChatStream(
      {
        message: content.trim(),
        session_id: sessionId.value,
        mode: chatMode.value,
        stream: true
      } as UnifiedChatRequest,
      // onEvent 回调
      (event: StreamChatEvent) => {
        const messageIndex = messages.value.findIndex(msg => msg.id === aiMessage.id);
        if (messageIndex === -1) return;

        const currentMessage = messages.value[messageIndex];

        switch (event.event) {
          case 'start':
            // 会话开始 - 显示初始状态
            messages.value[messageIndex] = {
              ...currentMessage,
              content: '智能体正在启动...',
              loading: true,
              isStreaming: true,
              timestamp: Date.now()
            };
            break;

          case 'message':
            // 状态消息 - 显示处理状态
            if (event.content) {
              messages.value[messageIndex] = {
                ...currentMessage,
                content: event.content,
                loading: true,
                isStreaming: true,
                timestamp: Date.now()
              };
            }
            break;

          case 'tool_call':
            // 工具调用 - 实时显示工具执行
            if (event.data) {
              const toolCall = event.data;
              if (!currentMessage.toolCalls) {
                currentMessage.toolCalls = [];
              }

              // 添加工具调用信息
              currentMessage.toolCalls.push({
                id: toolCall.tool_name + '_' + Date.now(),
                name: toolCall.tool_name,
                arguments: toolCall.arguments,
                result: toolCall.outputs,
                status: toolCall.success ? 'success' : 'error',
                error: toolCall.error_message,
                startTime: Date.now(),
                endTime: Date.now()
              });

              // 更新消息内容显示工具调用状态
              messages.value[messageIndex] = {
                ...currentMessage,
                content: `正在执行工具: ${toolCall.tool_name}`,
                loading: true,
                isStreaming: true,
                timestamp: Date.now()
              };
            }
            break;

          case 'response':
            // 最终响应 - 显示完整结果
            if (event.content) {
              fullResponse = event.content;
              const parsed = parseAIResponse(fullResponse);
              messages.value[messageIndex] = {
                ...currentMessage,
                content: parsed.actualContent,
                thinkingContent: parsed.thinkingContent,
                thinking: Boolean(parsed.hasThinking),
                thinkingStatus: 'end',
                loading: false,
                isStreaming: false,
                timestamp: Date.now()
              };
            }
            break;
        }
        scrollToBottom();
      },
      // onError 回调
      (error: Error) => {
        console.error('智能体聊天错误:', error);
        ElMessage.error('智能体聊天失败，请重试');

        // 移除失败的AI消息
        const messageIndex = messages.value.findIndex(msg => msg.id === aiMessage.id);
        if (messageIndex !== -1) {
          messages.value.splice(messageIndex, 1);
        }
        isLoading.value = false;
      },
      // onComplete 回调
      () => {
        // 最终完成处理
        const messageIndex = messages.value.findIndex(msg => msg.id === aiMessage.id);
        if (messageIndex !== -1) {
          messages.value[messageIndex] = {
            ...messages.value[messageIndex],
            thinkingStatus: 'end', // 完成状态
            loading: false,
            isStreaming: false, // 完成后停止流式状态
            timestamp: Date.now()
          };
          scrollToBottom();
        }
        isLoading.value = false;
      }
    );
  } catch (error) {
    console.error('AI聊天失败:', error);
    ElMessage.error('AI聊天失败，请重试');

    // 移除失败的AI消息
    const messageIndex = messages.value.findIndex(msg => msg.id === aiMessage.id);
    if (messageIndex !== -1) {
      messages.value.splice(messageIndex, 1);
    }
  } finally {
    isLoading.value = false;
  }
};



// 清空对话
const clearChat = async () => {
  try {
    await ElMessageBox.confirm(
      '确定要清空所有对话记录吗？此操作不可恢复。',
      '确认清空',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }
    );

    // 清空本地消息
    messages.value = [];

    // 清空远程会话历史
    try {
      await clearChatHistoryAPI(sessionId.value);
    } catch (error) {
      console.warn('清空远程会话历史失败:', error);
    }

    // 生成新的会话ID
    sessionId.value = aiUtils.generateSessionId();

    ElMessage.success(successMessages.historyCleaned);
  } catch {
    // 用户取消操作
  }
};

// 检查智能体服务健康状态
const checkAgentHealth = async () => {
  try {
    agentHealthStatus.value = 'checking';

    // 调用专用的健康检查端点（不使用大模型）
    const response = await fetch(`${fastApiRequest.defaults.baseURL}/api/iot/v1/chat/health`, {
      method: 'GET',
      headers: {
        'Authorization': getAuthorizationHeader() || '',
      }
    });

    if (response.ok) {
      const result = await response.json();
      if (result.code === 200 && result.data?.status === 'healthy') {
        agentHealthStatus.value = 'healthy';
        console.log('智能体服务连接正常:', result.data);
      } else {
        agentHealthStatus.value = 'unhealthy';
        console.warn('智能体服务状态异常:', result);
      }
    } else {
      agentHealthStatus.value = 'unhealthy';
      console.warn('智能体服务响应异常:', response.status);
    }
  } catch (error: any) {
    console.error('智能体服务健康检查失败:', error);
    agentHealthStatus.value = 'unreachable';
  }
};

// 智能体相关辅助函数
const getAgentDisplayName = (message: ChatMessage): string => {
  if (message.mode === 'agent') {
    return 'AI智能体';
  } else if (message.mode === 'ai_only') {
    return 'AI助手';
  }
  return 'AI助手';
};

const getAgentAvatar = (message: ChatMessage): string => {
  if (message.mode === 'agent') {
    return '🤖';
  }
  return 'AI';
};

const getModeTagType = (mode: string): string => {
  switch (mode) {
    case 'agent':
      return 'success';
    case 'ai_only':
      return 'info';
    case 'auto':
      return 'warning';
    default:
      return 'info';
  }
};

const getModeDisplayName = (mode: string): string => {
  switch (mode) {
    case 'agent':
      return '智能体';
    case 'ai_only':
      return '仅AI';
    case 'auto':
      return '自动';
    default:
      return '未知';
  }
};

// 格式化工具调用为ThoughtChain数据
const formatToolCallsForThoughtChain = (toolCalls: ToolCall[]) => {
  return toolCalls.map((toolCall, index) => ({
    id: index + 1,
    title: `调用工具: ${toolCall.name}`,
    thinkContent: `
参数: ${JSON.stringify(toolCall.arguments, null, 2)}
${toolCall.result ? `结果: ${JSON.stringify(toolCall.result, null, 2)}` : ''}
${toolCall.error ? `错误: ${toolCall.error}` : ''}
执行时间: ${toolCall.startTime && toolCall.endTime ? `${toolCall.endTime - toolCall.startTime}ms` : '未知'}
    `.trim(),
    status: toolCall.status,
    isDefaultExpand: toolCall.status === 'error',
    isCanExpand: true
  }));
};

// 工具调用展开事件处理
const onToolCallExpand = (item: any) => {
  console.log('工具调用展开:', item);
};

// 导出对话
const exportChat = () => {
  if (messages.value.length === 0) {
    ElMessage.warning('暂无对话记录可导出');
    return;
  }

  const chatContent = messages.value
    .filter(msg => !msg.loading)
    .map(msg => {
      const time = aiUtils.formatTime(msg.timestamp);
      const sender = msg.type === 'user' ? '用户' : getAgentDisplayName(msg);
      let content = `[${time}] ${sender}: ${msg.content}`;

      // 添加工具调用信息
      if (msg.toolCalls && msg.toolCalls.length > 0) {
        const toolInfo = msg.toolCalls.map(tool =>
          `  工具调用: ${tool.name}(${JSON.stringify(tool.arguments)})`
        ).join('\n');
        content += '\n' + toolInfo;
      }

      return content;
    })
    .join('\n\n');

  const blob = new Blob([chatContent], { type: 'text/plain;charset=utf-8' });
  const url = URL.createObjectURL(blob);
  const link = document.createElement('a');
  link.href = url;
  link.download = `${chatConfig.exportFilename}_${new Date().toLocaleDateString('zh-CN')}.txt`;
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
  URL.revokeObjectURL(url);

  ElMessage.success(successMessages.historyExported);
};

// 生命周期
onMounted(async () => {
  // 检查智能体服务健康状态
  await checkAgentHealth();

  // 智能体聊天界面初始化完成
  console.log('智能体聊天界面已准备就绪');

  // 定期检查智能体服务健康状态（每30秒）
  setInterval(checkAgentHealth, 30000);
});
</script>

<style scoped>
/* 卡片头部样式 */
.card-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 20px;
}

/* 聊天主容器 */
.chat-main-card {
  height: calc(100vh - 120px);
  display: flex;
  flex-direction: column;
}

.chat-main-card :deep(.el-card__body) {
  flex: 1;
  display: flex;
  flex-direction: column;
  padding: 0;
  overflow: hidden;
}

.chat-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* 消息显示区域 */
.chat-messages {
  flex: 1;
  overflow-y: auto;
  padding: 20px;
  background: linear-gradient(to bottom, #f8f9fa, #ffffff);
  border-bottom: 1px solid var(--el-border-color-light);
}

.empty-chat {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 消息项样式 */
.message-item {
  margin-bottom: 20px;
}

.message-wrapper {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  max-width: 80%;
}

.user-message {
  flex-direction: row-reverse;
  margin-left: auto;
}

.ai-message {
  margin-right: auto;
}

.message-avatar {
  flex-shrink: 0;
  margin-top: 4px;
}

.message-content {
  flex: 1;
  min-width: 0;
}

.message-text {
  background: var(--el-color-white);
  border: 1px solid var(--el-border-color-light);
  border-radius: 12px;
  padding: 12px 16px;
  word-wrap: break-word;
  line-height: 1.5;
  font-size: 14px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.user-message .message-text {
  background: var(--el-color-primary);
  color: white;
  border-color: var(--el-color-primary);
}

.ai-message .message-text {
  background: var(--el-color-white);
  border-color: var(--el-border-color-light);
}

.message-time {
  font-size: 12px;
  color: var(--el-text-color-secondary);
  margin-top: 4px;
  text-align: right;
}

.user-message .message-time {
  text-align: left;
}

.message-actions {
  margin-top: 8px;
  display: flex;
  gap: 8px;
}

/* 打字指示器 */
.typing-indicator {
  display: flex;
  align-items: center;
  gap: 4px;
}

.typing-indicator span {
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--el-color-primary);
  animation: typing 1.4s infinite ease-in-out;
}

.typing-indicator span:nth-child(1) {
  animation-delay: -0.32s;
}

.typing-indicator span:nth-child(2) {
  animation-delay: -0.16s;
}

@keyframes typing {
  0%, 80%, 100% {
    transform: scale(0.8);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 输入区域样式 */
.chat-input-area {
  flex-shrink: 0;
  padding: 20px;
  background: var(--el-color-white);
  border-top: 1px solid var(--el-border-color-light);
}

/* Element Plus X 组件样式定制 */
.editor-prefix {
  display: flex;
  align-items: center;
  gap: 8px;
}

.editor-actions {
  display: flex;
  align-items: center;
  gap: 8px;
}

/* EditorSender 样式覆盖 */
:deep(.el-editor-sender) {
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--el-border-color-light);
}

:deep(.el-editor-sender-content) {
  min-height: 60px;
  max-height: 200px;
  padding: 12px;
}

:deep(.el-editor-sender-action-list) {
  padding: 8px 12px;
  border-top: 1px solid var(--el-border-color-lighter);
}

/* Bubble 组件样式覆盖 */
:deep(.el-bubble) {
  margin-bottom: 0;
  max-width: 80%;
}

:deep(.el-bubble-user) {
  margin-left: auto;
}

:deep(.el-bubble-ai) {
  margin-right: auto;
}

:deep(.el-bubble-content) {
  line-height: 1.6;
  word-wrap: break-word;
}

/* Typewriter 组件样式 */
:deep(.typewriter-cursor) {
  animation: blink 1s infinite;
  color: var(--el-color-primary);
  font-weight: bold;
}

@keyframes blink {
  0%, 50% { opacity: 1; }
  51%, 100% { opacity: 0; }
}

/* 消息项动画 */
.message-item {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.input-card {
  border: none;
  box-shadow: none;
}

.input-card :deep(.el-card__body) {
  padding: 0;
}

.input-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.message-input :deep(.el-textarea__inner) {
  border: 1px solid var(--el-border-color);
  border-radius: 8px;
  padding: 12px;
  font-size: 14px;
  line-height: 1.5;
  resize: none;
  transition: border-color 0.3s;
}

.message-input :deep(.el-textarea__inner):focus {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px rgba(64, 158, 255, 0.2);
}

.input-actions {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.input-tips {
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.send-button {
  padding: 8px 20px;
  font-weight: 500;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .card-header {
    flex-direction: column;
    gap: 15px;
  }

  .message-wrapper {
    max-width: 90%;
  }

  .chat-main-card {
    height: calc(100vh - 100px);
  }
}

@media (max-width: 768px) {
  .chat-messages {
    padding: 15px;
  }

  .chat-input-area {
    padding: 15px;
  }

  .message-wrapper {
    max-width: 95%;
  }

  .message-text {
    padding: 10px 12px;
    font-size: 13px;
  }

  .input-actions {
    flex-direction: column;
    gap: 8px;
    align-items: stretch;
  }

  .send-button {
    width: 100%;
    padding: 12px;
    height: 44px;
  }

  .input-tips {
    text-align: center;
  }
}

@media (max-width: 480px) {
  .chat-main-card {
    height: calc(100vh - 80px);
  }

  .chat-messages {
    padding: 10px;
  }

  .chat-input-area {
    padding: 10px;
  }

  .message-wrapper {
    max-width: 100%;
  }

  .message-text {
    padding: 8px 10px;
    font-size: 12px;
  }

  .message-avatar {
    width: 32px;
    height: 32px;
  }

  .send-button {
    height: 48px;
    font-size: 14px;
  }
}

/* Element Plus X 组件样式定制 */
.message-item {
  margin-bottom: 16px;
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.message-actions {
  margin-top: 8px;
  opacity: 0.7;
  transition: opacity 0.2s ease;
}

.message-item:hover .message-actions {
  opacity: 1;
}

/* 思考组件样式 */
.thinking-chain-wrap {
  margin-bottom: 8px;
}

/* Mermaid 图表样式修复 */
:deep(.markdown-mermaid) {
  background: var(--el-bg-color);
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid var(--el-border-color-light);
}

:deep(.mermaid-content) {
  background: white !important;
  min-height: 200px;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px;
}

:deep(.mermaid-content svg) {
  max-width: 100%;
  height: auto;
  background: white !important;
}

/* 修复可能的黑色背景问题 */
:deep(.markdown-mermaid .mermaid-content) {
  background: white !important;
  color: #000 !important;
}

:deep(.markdown-mermaid .mermaid-content *) {
  background: transparent !important;
}

/* ECharts 图表样式 */
:deep(.echarts-chart) {
  background: white !important;
  border-radius: 6px;
  border: 1px solid var(--el-border-color-light);
  padding: 16px;
  margin: 8px 0 32px 0; /* 进一步增加底部间距，确保与代码操作按钮有足够距离 */
}

/* 修复代码块渲染问题 */
:deep(.markdown-code-block) {
  background: var(--el-bg-color-page) !important;
  border: 1px solid var(--el-border-color-light);
  border-radius: 6px;
  overflow: hidden;
}

:deep(.markdown-code-block .code-content) {
  background: white !important;
  min-height: 100px;
}

/* 聊天输入区域样式 */
.chat-input-area {
  padding: 20px;
  border-top: 1px solid var(--el-border-color-light);
}

/* Element Plus X Bubble 组件基础样式 */
:deep(.el-bubble) {
  margin-bottom: 16px;
}

:deep(.el-bubble-content) {
  max-width: 100%;
  word-wrap: break-word;
  line-height: 1.6;
}

/* Element Plus X EditorSender 组件基础样式 */
:deep(.editor-sender) {
  border-radius: 8px;
  border: 1px solid var(--el-border-color-light);
}

:deep(.editor-sender:focus-within) {
  border-color: var(--el-color-primary);
  box-shadow: 0 0 0 2px var(--el-color-primary-light-8);
}

/* Thinking 组件样式 */
.thinking-wrapper {
  margin-bottom: 8px;
}

:deep(.thinking-container) {
  background: var(--el-fill-color-extra-light);
  border-radius: 8px;
  padding: 12px;
  border-left: 3px solid var(--el-color-primary);
}

/* AI 消息内容样式 */
.ai-message-content {
  line-height: 1.6;
}

.plain-text {
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* XMarkdown 组件样式定制 */
:deep(.x-markdown) {
  font-size: 14px;
  line-height: 1.6;
}

:deep(.x-markdown h1),
:deep(.x-markdown h2),
:deep(.x-markdown h3),
:deep(.x-markdown h4),
:deep(.x-markdown h5),
:deep(.x-markdown h6) {
  margin: 16px 0 8px 0;
  font-weight: 600;
}

:deep(.x-markdown p) {
  margin: 8px 0;
}

:deep(.x-markdown code) {
  background: var(--el-fill-color-light);
  padding: 2px 4px;
  border-radius: 4px;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
}

:deep(.x-markdown pre) {
  background: var(--el-fill-color-light);
  padding: 12px;
  border-radius: 8px;
  overflow-x: auto;
  margin: 12px 0;
}

:deep(.x-markdown blockquote) {
  border-left: 4px solid var(--el-color-primary);
  padding-left: 12px;
  margin: 12px 0;
  color: var(--el-text-color-secondary);
}

:deep(.x-markdown ul),
:deep(.x-markdown ol) {
  padding-left: 20px;
  margin: 8px 0;
}

:deep(.x-markdown table) {
  border-collapse: collapse;
  width: 100%;
  margin: 12px 0;
}

:deep(.x-markdown th),
:deep(.x-markdown td) {
  border: 1px solid var(--el-border-color);
  padding: 8px 12px;
  text-align: left;
}

:deep(.x-markdown th) {
  background: var(--el-fill-color-light);
  font-weight: 600;
}

/* 滚动条样式 */
.chat-messages::-webkit-scrollbar {
  width: 6px;
}

.chat-messages::-webkit-scrollbar-track {
  background: var(--el-fill-color-light);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb {
  background: var(--el-border-color-dark);
  border-radius: 3px;
}

.chat-messages::-webkit-scrollbar-thumb:hover {
  background: var(--el-border-color-darker);
}

/* 工具调用容器样式 */
.tool-calls-container {
  margin-bottom: 12px;
  padding: 12px;
  background: var(--el-fill-color-extra-light);
  border-radius: 8px;
  border-left: 4px solid var(--el-color-primary);
}

/* 消息头部信息样式 */
.message-header-info {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
  font-size: 12px;
  color: var(--el-text-color-secondary);
}

.agent-name {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

.message-time {
  margin-left: auto;
}

/* ThoughtChain 样式定制 */
:deep(.thought-chain) {
  background: transparent;
}

:deep(.thought-chain .el-timeline-item__content) {
  padding-left: 16px;
}

:deep(.thought-chain .el-timeline-item__title) {
  font-weight: 600;
  color: var(--el-text-color-primary);
}

:deep(.thought-chain .el-timeline-item__description) {
  color: var(--el-text-color-regular);
  white-space: pre-wrap;
  font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
  font-size: 12px;
  background: var(--el-fill-color-light);
  padding: 8px;
  border-radius: 4px;
  margin-top: 4px;
}
</style>